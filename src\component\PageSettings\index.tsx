import React, {FC, Fragment} from 'react';
import './index.scss';
import {toast, Input as TextInput, Checkbox, Radios} from 'amis';

// 图片引入
import btn_icon from './image/page_btn_icon.png';
import mssage_icon from './image/page_mssage_icon.png';
import nav_icon from './image/page_nav_icon.png';
import permissions_icon from './image/page_permissions_icon.png';
import print_icon from './image/page_print_icon.png';
import qrCode_icon from './image/page_qrCode_icon.png';
import set_icon from './image/page_set_icon.png';

import app_default from '@/image/common_icons/app_default_icon.png';

import AMISRenderer from '@/component/AMISRenderer';
// 引入修改应用名称弹窗
import {modifyPageNamePopup} from './common/modifyPageNamePopup';

import {modifyPageDescPopup} from './common/modifyPageDescPopup';

import {updateApplicationPageAndClass,amisCodeUpdate,amisCodeGet} from '@/utils/api/api';

import {createReportObj} from '@/utils/schemaPageTemplate/createPageObjs';
import PagePermission from '@/views/applySettings/component/pagePermission/index';

const PageSettings: FC<any> = (props: any) => {
  React.useEffect(() => {
    
  },[props.history.location])
  
  /* data 数据 */
  const menuList = [
    {
      label: '基础设置',
      icon: set_icon,
      to: 'baseSetting'
    },
    {
      label: '自定义详情页',
      icon: nav_icon,
      to: 'detailPage'
    },
    {
      label: '自定义按钮',
      icon: btn_icon,
      to: 'customButtons'
    },
    {
      label: '消息通知',
      icon: mssage_icon,
      to: 'messageSetting'
    },
    {
      label: '打印设置',
      icon: print_icon,
      to: 'printSetting'
    },
    // {
    //   title: '关联列表',
    //   icon: print_icon,
    //   path: 'associationList'
    // },
    {
      label: '权限设置-',
      icon: permissions_icon,
      to: 'permission'
    },
    // {
    //   title: '内置变量',
    //   icon: print_icon,
    //   path: 'builtinVars'
    // },
    {
      label: '二维码标签',
      icon: qrCode_icon,
      to: 'labelCode'
    }
  ];

  // 选中的菜单item
  const [activeItem, setActiveItem] = React.useState<any>(menuList[0]);

  const RadiosOptions = [
    {
      label: '默认页面',
      value: '1'
    },
    {
      label: '应用内页面',
      value: '2'
    },
    {
      label: '外部链接',
      value: '3'
    }
  ];
  // 打开修改页面名称弹窗
  const [openPageNamePopup, setOpenPageNamePopup] = React.useState(false);
  // 打开修改页面描述弹窗
  const [openPageDescPopup, setOpenPageDescPopup] = React.useState(false);

   
  /* data 数据 end */

  /* methods 方法 */
  // editRute
  const modifyPageName = (val: any) => {
    if (props.pageData.name == val[0].name) {
      setOpenPageNamePopup(false);
      return;
    }
    let data = {
      id: props.pageData.id,
      name: val[0].name,
      applicantOrBackend: 1
    };
    updateApplicationPageAndClass(data).then((res: any) => {
      if (res.code === 0) {
        toast.success('修改成功');
        setOpenPageNamePopup(false);
        props.update(data);
      } else {
        toast.error(res.msg);
      }
    });
  };

  const modifyPageDesc = (val: any) => {
    if (props.pageData.description === val[0].name) {
      setOpenPageDescPopup(false);
      return;
    }
    let data = {
      id: props.pageData.id,
      description: val[0].name,
      applicantOrBackend: 1
    };
    updateApplicationPageAndClass(data).then((res: any) => {
      if (res.code === 0) {
        toast.success('修改成功');
        setOpenPageDescPopup(false);
        props.update(data);
      } else {
        toast.error(res.msg);
      }
    });
  };

  // 设置活动菜单项并更新URL
  const handleMenuItemClick = (item: any) => {
    // 更新URL参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeMenu', item.to);
    console.log(1111111111111111111111111111111111111111111111111);
    console.log(urlParams.toString());
    console.log(props.history);
    // 使用history.replace更新URL而不创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };

    // 自定义渲染器，用于显示自定义图标
    const navItemRender = (item: any, index: number) => {
      return {
        type: 'flex',
        className: 'nav-item-custom',
        items: [
          {
            type: 'image',
            src: item.customIcon,
            className: 'pageSettingsBox-aside-item-img'
          },
          {
            type: 'tpl',
            tpl: '${label}'
          }
        ]
      };
    };

    // MyNav组件的配置
    const navSchema = {
      type: 'my-nav',
      stacked: true, // 垂直布局
      className: 'pageSettingsBox-aside',
      links: menuList,
      itemRender: navItemRender,
      onSelect: (item: any) => handleMenuItemClick(item),
      activeKey: activeItem.to
    };

  React.useEffect(() => {
    // 从URL参数中获取activeMenu
    const urlParams = new URLSearchParams(props.history.location.search);
    const activeMenuPath = urlParams.get('activeMenu');
    
    // 如果URL中有activeMenu参数，则设置对应的菜单项为活动项
    if (activeMenuPath) {
      const menuItem = menuList.find(item => item.to === activeMenuPath);
      if (menuItem) {
        setActiveItem(menuItem);
      }
    }
  }, [props.history.location]);

  

  return (
    <div className="pageSettingsBox">
      {/* <div className="pageSettingsBox-aside">
        {menuList.map((item: any,index:any) => {
          return (
            <div
              key={`${item.path}-${index}`}
              className={
                activeItem.path == item.path
                  ? 'pageSettingsBox-aside-item active_item'
                  : 'pageSettingsBox-aside-item'
              }
              onClick={() => handleMenuItemClick(item)}
            >
              <img className="pageSettingsBox-aside-item-img" src={item.icon} />
              <span>{item.title}</span>
            </div>
          );
        })}
      </div> */}

      <AMISRenderer
        schema={navSchema}
        data={{}}
      />
      <div className="pageSettingsBox-content">
        {activeItem.path == 'baseSetting' && (
          <Fragment>
            {/* 基础设置 */}
            <div
              className="pageSettingsBox-content-title"
              style={{marginTop: 0}}
            >
              <div className="pageSettingsBox-content-title-name">基础设置</div>
              <div className="pageSettingsBox-content-title-desc">
                设置页面名称、页面图标等
              </div>
            </div>
            {/* 页面图标 */}
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  页面图标
                </div>
                <div className="pageSettingsBox-content-item-left-icon">
                  <img
                    className="pageSettingsBox-content-item-left-icon-img"
                    src={app_default}
                  />
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
                <div className="pageSettingsBox-content-item-right-btn">
                  修改图标
                </div>
              </div>
            </div>
            {/* 页面名称 */}
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  页面名称
                </div>
                <div className="pageSettingsBox-content-item-left-content">
                  {props.pageData.name}
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
                <div
                  className="pageSettingsBox-content-item-right-btn"
                  onClick={() => setOpenPageNamePopup(true)}
                >
                  修改名称
                </div>
              </div>
            </div>
            {/* 页面描述 */}
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  页面描述
                </div>
                <div className="pageSettingsBox-content-item-left-content">
                  {props.pageData.description}
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
                <div
                  className="pageSettingsBox-content-item-right-btn"
                  onClick={() => setOpenPageDescPopup(true)}
                >
                  修改描述
                </div>
              </div>
            </div>

            {/* 常用设置 */}
            <div className="pageSettingsBox-content-title">
              <div className="pageSettingsBox-content-title-name">常用设置</div>
              <div className="pageSettingsBox-content-title-desc">
                设置数据标题、页面操作等
              </div>
            </div>
            {/* 数据标题 */}
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  数据标题
                </div>
                <div className="pageSettingsBox-content-item-left-content">
                  <TextInput
                    placeholder="请输入数据标题"
                    type="text"
                    value={'111'}
                    className="pageSettingsBox-content-item-left-content-input"
                  />
                </div>
              </div>
            </div>
            {/* 页面操作 */}
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  页面操作
                </div>
                <div className="pageSettingsBox-content-item-left-content">
                  {/* value={true} */}
                  <Checkbox
                    label="复制数据"
                    onChange={(value: any) => {
                      console.log(value);
                    }}
                  />
                </div>
              </div>
            </div>
            {/* 设置咨询人员 */}
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  设置咨询人员
                </div>
                <div className="pageSettingsBox-content-item-left-content">
                  <Checkbox
                    label="咨询人员为空时默认显示应用管理员"
                    onChange={(value: any) => {
                      console.log(value);
                    }}
                  />
                </div>
              </div>
            </div>
            {/* 页面提交后跳转的页面 */}
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  页面提交后跳转的页面
                </div>
                <div className="pageSettingsBox-content-item-left-content">
                  <Radios options={RadiosOptions} />
                </div>
              </div>
            </div>

            {/* 高级设置 */}
            <div className="pageSettingsBox-content-title">
              <div className="pageSettingsBox-content-title-name">高级设置</div>
              <div className="pageSettingsBox-content-title-desc">
                设置隐藏导航、页头样式等
              </div>
            </div>
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  <Checkbox
                    label="隐藏导航（不显示顶部导航）"
                    onChange={(value: any) => {
                      console.log(value);
                    }}
                  />
                </div>
                <div className="pageSettingsBox-content-item-left-content">
                  <Checkbox
                    label="开启群插件通知"
                    onChange={(value: any) => {
                      console.log(value);
                    }}
                  />
                </div>
              </div>
            </div>

            {/* 页头样式(手机端) */}
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  页头样式(手机端)
                </div>
                <div className="pageSettingsBox-content-item-left-content">
                  <Radios options={RadiosOptions} />
                </div>
              </div>
            </div>
          </Fragment>
        )}

        {activeItem.path == 'printSetting' && (
          <Fragment>
            {(
              <AMISRenderer
                schema={createReportObj(
                  `https://javaddm.fjpipixia.com/wflow/#/workspace/printSetting?pageID=${props.pageData.id}&userID=${props.store.userInfo?.id}&applyID=${props.pageData.apply_id}&pageName=${props.pageData.name}&tenantId=${props.store.tenant_id}&authorization=${props.store.access_token}`
                )}
                embedMode={true}
              />
            )}
          </Fragment>
        )}


        {activeItem.path == 'permission' && (
              <PagePermission
                pageData={props.pageData}
                history={props.history}
                match={props.match}
              />
        )}

      </div>

      {openPageNamePopup && (
        <AMISRenderer
          show={openPageNamePopup}
          schema={modifyPageNamePopup(props.pageData.name)}
          onClose={() => setOpenPageNamePopup(false)}
          onConfirm={(val: any) => modifyPageName(val)}
        />
      )}
      {openPageDescPopup && (
        <AMISRenderer
          show={openPageDescPopup}
          schema={modifyPageDescPopup(props.pageData.description)}
          onClose={() => setOpenPageDescPopup(false)}
          onConfirm={(val: any) => modifyPageDesc(val)}
        />
      )}
    </div>
  );
};

export default PageSettings;

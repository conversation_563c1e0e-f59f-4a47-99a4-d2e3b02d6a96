import {get, del, post, put} from '@/utils/services/request';
//wflow
// 查询用户架构树
interface GetUserTreeData {
  [key: string]: any;
}
const getUserTree = (params: GetUserTreeData): Promise<any> =>
  get('/admin-api/system/user/page', params);
export {getUserTree};

// 查询部门架构树
interface GetDepartmentTreeData {
  [key: string]: any;
}
const getDepartmentTree = (params: GetDepartmentTreeData): Promise<any> =>
  get('/admin-api/system/dept/list', params);
export {getDepartmentTree};

// 查询角色架构树
interface GetRoleTreeData {
  [key: string]: any;
}
const getRoleTree = (params: GetRoleTreeData): Promise<any> =>
  get('/admin-api/system/role/page', params);
export {getRoleTree};

// 获取用户简易列表
const getUserList = (): Promise<any> =>
  get(`/admin-api/system/user/simple-list`);
export {getUserList};

// 获取树状用户列表
const getTreeUserList = (): Promise<any> =>
  // get(`/admin-api/system/user/pageNew`);
  get(`/admin-api/system/user/list-dept-user`);
export {getTreeUserList};

// 获取树状部门列表
const getTreeDeptList = (): Promise<any> =>
  get(`/admin-api/system/dept/list-all-nested`);
export {getTreeDeptList};

// 获取角色列表
const getRoleList = (): Promise<any> =>
  get(`/admin-api/system/role/page?pageNo=1&pageSize=100`);
export {getRoleList};

// 获取岗位列表
const getPosList = (): Promise<any> =>
  get(`/admin-api/system/post/page?pageNo=1&pageSize=100`);
export {getPosList};

// 保存流程
interface saveProcessData {
  [key: string]: any;
}
const saveProcess = (data: saveProcessData, isSub = false): Promise<any> =>
  post(`/admin-api/wflow/process/model${isSub ? '/sub' : ''}/save`, data);
export {saveProcess};

// 发布流程
const deployProcess = (code: string, isSub = false): Promise<any> =>
  post(`/admin-api/wflow/process/model${isSub ? '/sub' : ''}/deploy/${code}`);
export {deployProcess};

//  获取子流程模型
interface getGroupModelsData {
  [key: string]: any;
}
const getGroupModels = (
  data: getGroupModelsData,
  isSub = false
): Promise<any> =>
  post(`/admin-api/wflow/model/${isSub ? 'sub/' : ''}group/list`, data);
export {getGroupModels};

// 启动流程
interface startProcessData {
  applicationPageId: number;
  tenantId: string;
  deptId: string;
  formData: {};
  processUsers: {};
  [key: string]: any;
}
const startProcess = (code: string, data: startProcessData): Promise<any> =>
  post(`/admin-api/wflow/process/start/${code}`, data);
export {startProcess};

// 获取流程模型详情
const getModelById = (modelId: string): Promise<any> =>
  get(`/admin-api/wflow/model/detail/${modelId}`);
export {getModelById};

// 账号密码登录接口/auth/login
interface accountLoginData {
  username: string;
  password?: string;
  [key: string]: any;
}
const accountLogin = (data: accountLoginData): Promise<any> =>
  post('/admin-api/system/auth/login', data);
export {accountLogin};

// 短信验证码登录接口/auth/sms-login
interface smsLoginData {
  mobile: string;
  code: string;
  [key: string]: any;
}
const smsLogin = (data: smsLoginData): Promise<any> =>
  post('/admin-api/system/auth/sms-login', data);
export {smsLogin};

// 注册接口/auth/register
interface registerData {
  username: string;
  code: string;
  [key: string]: any;
}
const register = (data: registerData): Promise<any> =>
  post('/admin-api/system/auth/register', data);
export {register};

// 短信验证码登录接口/sms-template/send-sms
interface sendSmsData {
  mobile: string;
  scene: string;
  [key: string]: any;
}
const sendSms = (data: sendSmsData): Promise<any> =>
  post('/admin-api/system/auth/send-sms-code', data);
export {sendSms};

// 微信扫码登录接口/auth/social-login
interface socialLoginData {
  code: string;
  type: string;
  [key: string]: any;
}
const socialLogin = (data: socialLoginData): Promise<any> =>
  post('/admin-api/system/auth/social-login', data);
export {socialLogin};

// 微信绑定接口/social-user/bind
interface socialBindData {
  code: string;
  type: string;
  [key: string]: any;
}
const socialBind = (data: socialBindData): Promise<any> =>
  post('/admin-api/system/social-user/bind', data);
export {socialBind};

// 微信解绑接口/social-user/unbind
interface socialUnBindData {
  openid: string;
  type: string;
  [key: string]: any;
}
export function socialUnBind(params: socialUnBindData): Promise<any> {
  return post('/admin-api/system/social-user/unbind', params);
}

// dmin-api/system/user/profile/get 获取租户信息
const getUserProfile = (): Promise<any> =>
  get('/admin-api/system/user/profile/get');
export {getUserProfile};

// dmin-api/system/user/profile/update 更新租户信息
interface updateUserProfileData {
  [key: string]: any;
}
const updateUserProfile = (data: updateUserProfileData): Promise<any> =>
  put('/admin-api/system/user/profile/update', data);
export {updateUserProfile};

//获取租户详情
interface getTenantData {
  id: string | number; //应用编号,示例值(28561)
}
export function getTenant(data: getTenantData): Promise<any> {
  return get(`/admin-api/system/tenant/get`, data);
}

//根据用户名获取租户列表
interface getTenantListData {
  [key: string]: any;
}
export function getTenantList(
  data: getTenantListData
): Promise<any> {
  return get(`/admin-api/system/tenant/getTenantList`, data);
}

// 创建租户
interface createTenantData {
  userId: string; //用户id
  name: string; //企业全称
  logo?: string; //企业logo
  packageId: string;
  [key: string]: any;
}
const createTenant = (data: createTenantData): Promise<any> =>
  post('/admin-api/system/tenant/create-new', data);
export {createTenant};

// 更新租户
interface updateTenantData {
  [key: string]: any;
}
const updateTenant = (data: updateTenantData): Promise<any> =>
  put('/admin-api/system/tenant/update', data);
export {updateTenant};

// dmin-api/system/user/project/update 更新项目或团队信息
interface updateProjectData {
  [key: string]: any;
}
const updateTeamProject = (data: updateProjectData): Promise<any> =>
  put('/admin-api/system/team-and-project/update', data);
export {updateTeamProject};

//上传图标
const updateIcon = (data: FormData): Promise<any> =>
  //return post('https://dxshy.duxieshe.com/api/task/test1', data);
  post('/admin-api/infra/file/upload', data);
export {updateIcon};

// dmin-api/system/user/profile/update-avatar 更新用户头像
const updateUserAvatar = (data: FormData): Promise<any> =>
  post('/admin-api/system/user/profile/update-avatar', data);
export {updateUserAvatar};

/**
 * 创建团队和项目
 * @param {object} params 管理后台 - 团队和项目新增/修改 Request VO
 * @param {number} params.parentId 父级编号 团队：0  项目: 团队id
 * @param {string} params.name 名称
 * @param {string} params.description 描述
 * @param {string} params.icon 团队图标
 * @returns
 */
export interface CreateTeamAndProjectParams {
  /*父级编号 */
  parentId?: number;

  /*名称 */
  name?: string;

  /*描述 */
  description?: string;

  /*团队图标 */
  icon?: string;
}
export function createTeamAndProject(
  params: CreateTeamAndProjectParams
): Promise<any> {
  return post(`/admin-api/system/team-and-project/create`, params);
}

/**
 * 获取团队和项目列表
 * @returns
 */
export function getTeamAndProjectList(): Promise<any> {
  return get(`/admin-api/system/team-and-project/list`);
}
/**
 * 获取团队和项目
 * @param {string} id 编号
 * @returns
 */
interface GetTeamAndProjectParams {
  id: number; //编号,示例值(28561)
}
export function getTeamAndProject(
  params: GetTeamAndProjectParams
): Promise<any> {
  return get(`/admin-api/system/team-and-project/get`, params);
}

/**
 * 获取团队成员
 * @param {string} id 编号
 * @returns
 */
interface GetTeamMemberParams {
  teamOrProjectOrApplicationId: string | number; //编号,示例值(28561)
  userId: string | number; //用户编号,示例值(1)
  pageNo: number; //页码
  pageSize: number; //每页数量
  type: string | number; //类型,示例值(1团队2项目3应用)
}

export function getTeamMember(params: GetTeamMemberParams): Promise<any> {
  return get(`/admin-api/system/team-project-application-members/page`, params);
}

// 选择的成员
interface selectMmberData {
  teamOrProjectOrApplicationId: string | number;
}
const selectMember = (data: selectMmberData): Promise<any> =>
  get(
    '/admin-api/system/team-project-application-members/get-selected-users',
    data
  );
export {selectMember};

/**
 * 删除团队和项目
 * @param {string} id 编号
 * @returns
 */

interface DeleteTeamAndProjectParams {
  id: number; //编号,示例值(28561)
}

export function deleteTeamAndProject(
  params: DeleteTeamAndProjectParams
): Promise<any> {
  return del(`/admin-api/system/team-and-project/delete`, params);
}

// Parameter interface
export interface UpdateTeamAndProjectParams {
  /*团队或者项目编号 */
  id: number;

  /*父级编号 */
  parentId?: number;

  /*名称 */
  name?: string;

  /*描述 */
  description?: string;

  /*团队图标 */
  icon?: string;

  /*是否放入回收站(1是，0否) */
  recycle?: number;
}
/**
 * 更新团队和项目
 * @param {object} params 管理后台 - 团队和项目新增/修改 Request VO
 * @param {number} params.id 团队或者项目编号
 * @param {number} params.parentId 父级编号
 * @param {string} params.name 名称
 * @param {string} params.description 描述
 * @param {string} params.icon 团队图标
 * @param {number} params.recycle 是否放入回收站(1是，0否)
 * @returns
 */
export function updateTeamAndProject(
  params: UpdateTeamAndProjectParams
): Promise<any> {
  return put(`/admin-api/system/team-and-project/update`, params);
}

/**
 * 创建应用
 * @param {object} params 管理后台 - 应用新增/修改 Request VO
 * @param {number} params.id 应用编号
 * @param {number} params.projectId 项目编号
 * @param {string} params.name 项目名称
 * @param {string} params.description 项目描述
 * @returns
 */
// Parameter interface
export interface javaApplicationCreateData {
  /*项目编号 */
  projectId: number;
  /*项目名称 */
  name: string;
  /*项目描述 */
  description?: string;
}
const javaApplicationCreate = (
  params: javaApplicationCreateData
): Promise<any> => post(`/admin-api/system/application/create`, params);
export {javaApplicationCreate};

/**
 * 删除应用
 * @param {string} id 编号
 * @returns
 */
interface javaDeleteApplicationData {
  id: string | number; //应用编号,示例值(28561)
}
export function javaApplicationDelete(
  data: javaDeleteApplicationData
): Promise<any> {
  return del(`/admin-api/system/application/delete`, data);
}

/**
 * 获得应用
 * @param {string} id 编号
 * @returns
 */
interface javaApplicationData {
  id: string | number; //应用编号,示例值(28561)
}
export function javaApplication(data: javaApplicationData): Promise<any> {
  return get(`/admin-api/system/application/get`, data);
}

/**
 * 获得应用页面
 * @param {string} id 编号
 * @returns
 */
interface javaApplicationListData {
  projectId?: string | number; //项目编号
  pageNo?: number; //页码
  pageSize?: number; //每页数量
  creator?: string | number; // 创建者编号
  isRelease?: number; // 是否发布
}
export function javaApplicationList(
  data: javaApplicationListData
): Promise<any> {
  return get(`/admin-api/system/application/page`, data);
}

/**
 * 获得最近使用应用
 * @returns
 */
export function recentlyUsedApplicationList(): Promise<any> {
  return get(`/admin-api/system/application/recently-used`);
}


/**
 * 更新应用
 * @param {object} params 管理后台 - 应用新增/修改 Request VO
 * @param {number} params.id 应用编号
 * @param {number} params.projectId 项目编号
 * @param {string} params.name 项目名称
 * @param {string} params.description 项目描述
 * @returns
 */
// Parameter interface
export interface javaApplicationUpdateData {
  /*应用编号 */
  id: number;

  /*项目编号 */
  // projectId: number;

  // /*项目名称 */
  // name: string;

  // /*项目描述 */
  // description?: string;
}

export function javaApplicationUpdate(
  data: javaApplicationUpdateData
): Promise<any> {
  return put(`/admin-api/system/application/update`, data);
}

// 应用页面接口

// 获取应用分类与页面列表 /admin-api/system/application-page-and-class/list
// Parameter interface
export interface getApplicationPageAndClassListParams {
  /*应用编号 */
  applicationId?: number;
  //1应用内 2后台
  applicantOrBackend: number;
}
export function getApplicationPageAndClassList(
  params: getApplicationPageAndClassListParams
): Promise<any> {
  return get(`/admin-api/system/application-page-and-class/list`, params);
}

// 获取应用分类与页面列表 /admin-api/system/application-page-and-class/list
// Parameter interface
export interface getApplicationPageAndClassBackendListParams {
  /*应用编号 */
  applicationId?: number;
  //1应用内 2后台
  applicantOrBackend: number;

  /*页码，从 1 开始 */
  pageNo?: number;

  /*每页条数，最大值为 100 */
  pageSize?: number;
}
export function getApplicationPageAndClassBackendList(
  params: getApplicationPageAndClassBackendListParams
): Promise<any> {
  return get(
    `/admin-api/system/application-page-and-class/backend-list`,
    params
  );
}

// Parameter interface
export interface CreateApplicationPageAndClassParams {
  /*应用编号 */
  applicationId?: number;

  /*父级编号 */
  parentId?: number;
  //1应用内 2后台
  applicantOrBackend: number;

  /*页面/分类(1页面2分类) */
  type?: number;

  /*排序 */
  sort?: number;

  /*名称 */
  name?: string;

  /*页面类型 */
  pageType?: number;

  /*页面链接 */
  url?: string;

  /*数据源id */
  dataSetId?: string | number;
}

/**
 * 创建应用页面和分类
 * @param {object} params 管理后台 - 应用页面和分类新增/修改 Request VO
 * @param {number} params.applicationId 应用编号
 * @param {number} params.applicantOrBackend //1应用内 2后台
 * @param {number} params.parentId 父级编号
 * @param {number} params.type 页面/分类(1页面2分类)
 * @param {number} params.sort 排序
 * @param {string} params.name 名称
 * @param {number} params.pageType 页面类型
 * @param {string} params.url 页面链接
 * @param {number} params.dataSetId 数据源id
 * @returns
 */
export function createApplicationPageAndClass(
  params: CreateApplicationPageAndClassParams
): Promise<any> {
  return post(`/admin-api/system/application-page-and-class/create`, params);
}

export interface getApplicationPageAndClassParams {
  // 页面/页面分类id
  id: number | string;
}

/**
 * 获得应用页面和分类
 * @param {string} id 编号
 * @returns
 */
export function getApplicationPageAndClass(
  params: getApplicationPageAndClassParams
): Promise<any> {
  return get(`/admin-api/system/application-page-and-class/get`, params);
}

export interface getControlDictParams {
  // 页面/页面分类id
  id: number | string;
}

/**
 * 获得控件字典
 * @param {string} id 编号
 * @returns
 */
export function getControlDict(params: getControlDictParams): Promise<any> {
  return get(`/admin-api/system/control-dict/get`, params);
}

export interface getComponentTemplateParams {
  // 组件模版id
  id: number | string;
}

/**
 * 获得组件模版
 * @param {string} id 编号
 * @returns
 */
export function getComponentTemplate(
  params: getComponentTemplateParams
): Promise<any> {
  return get(`/admin-api/system/component-template/get`, params);
}
export interface UpdateComponentTemplateParams {
  /*组件模版编号 */
  id: number;

  /*分类编号 */
  classificationId?: number;

  /*名称 */
  name?: string;

  /*描述 */
  describe?: string;

  /*schema */
  jsonData?: string;

  /*封面图片 */
  coverImage?: string;
}

/**
 * 更新组件模版
 * @param {object} params 管理后台 - 组件模版新增/修改 Request VO
 */
export function updateComponentTemplate(
  params: UpdateComponentTemplateParams
): Promise<any> {
  return put(`/admin-api/system/component-template/update`, params);
}

// Parameter interface
export interface UpdateApplicationPageAndClassParams {
  /*应用页面和分类编号 */
  id: number | string;
  /*应用编号 */
  applicationId?: number;

  /*父级编号 */
  parentId?: number;

  /*页面/分类(1页面2分类) */
  type?: number;

  /*1应用内 2后台 */
  applicantOrBackend?: number;

  /*排序 */
  sort?: number;

  /*名称 */
  name?: string;

  /*描述 */
  description?: string;
  /*页面类型 */
  pageType?: number;

  /*页面链接 */
  url?: string;

  /*数据源id */
  dataSetId?: string | number;

  formDataId?: string | number;
}

/**
 * 更新应用页面和分类
 * @param {object} params 管理后台 - 应用页面和分类新增/修改 Request VO
 * @param {number} params.id 应用页面和分类编号
 * @param {number} params.applicationId 应用编号
 * @param {number} params.applicantOrBackend //1应用内 2后台
 * @param {number} params.parentId 父级编号
 * @param {number} params.type 页面/分类(1页面2分类)
 * @param {number} params.sort 排序
 * @param {string} params.name 名称
 * @param {string} params.description 描述
 * @param {number} params.pageType 页面类型
 * @param {string} params.url 页面链接
 * @param {number} params.dataSetId 数据源id
 * @returns
 */
export function updateApplicationPageAndClass(
  params: UpdateApplicationPageAndClassParams
): Promise<any> {
  return put(`/admin-api/system/application-page-and-class/update`, params);
}

export interface deleteApplicationPageAndClassParams {
  /*应用页面和分类编号 */
  id: number;

  /*页面/分类(1页面2分类) */
  type: number;

  /*操作类型(1取消分类，2删除) */
  operateType: number;
}
/**
 * 删除应用页面和分类 \ 取消分类
 * @param {object} params 管理后台 - 删除应用页面和分类 \ 取消分类
 * @param {number} params.id 应用页面和分类编号
 * @param {number} params.type 页面/分类(1页面2分类)
 * @param {number} params.operateType 操作类型(1取消分类，2删除)
 * @returns
 */
export function deleteApplicationPageAndClass(
  params: deleteApplicationPageAndClassParams
): Promise<any> {
  return del(`/admin-api/system/application-page-and-class/delete`, params);
}

// Parameter interface
export interface CreateFormDataParams {
  /*应用页面编号 */
  applicationPageId?: number;

  /*表单数据 */
  data?: string;

  /*表单字段数据 */
  field?: string;
}
/**
 * 创建表单数据
 * @param {object} params 管理后台 - 表单数据新增/修改 Request VO
 * @param {number} params.applicationPageId 应用页面编号
 * @param {string} params.data 表单数据
 * @param {string} params.field 表单字段数据
 * @returns
 */
export function createFormData(params: CreateFormDataParams): Promise<any> {
  return post(`/admin-api/system/form-data/create`, params);
}


export interface CreateFormFieldValueDataParams {
  /*应用页面编号 */
  dynamicFields?: any;
}
export function createFormFieldValueData(applicationPageld: string|number, params: CreateFormFieldValueDataParams): Promise<any> {
  return post(`/admin-api/system/form-field-value/create/${applicationPageld}`, params);
}

interface getFormFieldPageParams {
  applicationPageId?: number; //应用页面编号
  dataSetId?: string|number;
  pageNo?: number; //页码，从 1 开始
  pageSize?: number; //每页条数，最大值为 100
}

/**
 * 获得表单字段分页
 * @param {object} params 参数对象
 * @param {number} params.applicationPageId 应用页面编号
 * @param {string|number} params.dataSetId 数据集ID
 * @param {number} params.pageNo 页码，从 1 开始
 * @param {number} params.pageSize 每页条数，最大值为 100
 * @returns
 */
export function getFormFieldPage(params: getFormFieldPageParams): Promise<any> {
  return get(`/admin-api/system/form-field/page`, params);
}

interface getFormDataPageParams {
  applicationPageId: number; //应用页面编号
  data?: string; //表单数据
  field?: string; //表单字段数据
  createTime?: string; //创建时间
  pageNo: number; //页码，从 1 开始
  pageSize: number; //每页条数，最大值为 100
}

/**
 * 获得表单数据分页
 * @param {string} applicationPageId 应用页面编号
 * @param {string} data 表单数据
 * @param {string} field 表单字段数据
 * @param {string} createTime 创建时间
 * @param {string} pageNo 页码，从 1 开始
 * @param {string} pageSize 每页条数，最大值为 100
 * @returns
 */
export function getFormDataPage(params: getFormDataPageParams): Promise<any> {
  return get(`/admin-api/system/form-data/page`, params);
}

interface getComponentClassificationPageParams {
  pageNo: number; //页码，从 1 开始
  pageSize: number; //每页条数
  type: number | string;
}
export function getComponentClassificationPage(
  params: getComponentClassificationPageParams
): Promise<any> {
  return get(`/admin-api/system/component-classification/page`, params);
}

export function getImageClassificationPage(params: getComponentClassificationPageParams): Promise<any> {
  return get(`/admin-api/system/image-classification/page`, params);
}


interface createComponentClassificationParams {
  name: string; //分类名称
  type: number | string;
}
export function createComponentClassification(
  params: createComponentClassificationParams
): Promise<any> {
  return post(`/admin-api/system/component-classification/create`, params);
}
interface getSystemComponentClassificationPageParams {
  pageNo: number; //页码，从 1 开始
  pageSize: number; //每页条数
}
export function getSystemComponentClassificationPage(
  params: getSystemComponentClassificationPageParams
): Promise<any> {
  return get(`/admin-api/system/component-manager-classification/page`, params);
}

export function getOrgComponentClassificationPage(
  params: getSystemComponentClassificationPageParams
): Promise<any> {
  return get(`/admin-api/system/control-dict-classification/page`, params);
}



interface createSystemComponentClassificationParams {
  name: string; //分类名称
}
export function createSystemComponentClassification(
  params: createSystemComponentClassificationParams
): Promise<any> {
  return post(`/admin-api/system/component-manager-classification/create`, params);
}



interface updateComponentClassificationParams {
  id: number;
  name: string; //分类名称
}
export function updateComponentClassification(
  params: updateComponentClassificationParams
): Promise<any> {
  return put(`/admin-api/system/component-classification/update`, params);
}
interface deleteComponentClassificationParams {
  id: number; //id
}
export function deleteComponentClassification(
  params: deleteComponentClassificationParams
): Promise<any> {
  return del(`/admin-api/system/component-classification/delete`, params);
}

export function deleteImageClassification(params: deleteComponentClassificationParams): Promise<any> {
  return del(`/admin-api/system/image-classification/delete`, params);
}


// admin-api/infra/file/page?pageNo=1&pageSize=10
interface getFilePageParams {
  pageNo: number; //页码，从 1 开始
  pageSize: number; //每页条数
}
export function getFilePage(params: getFilePageParams): Promise<any> {
  return get(`/admin-api/infra/file/page`, params);
}
// Parameter interface
export interface UpdateFormDataParams {
  /*表单数据编号 */
  id: number;

  /*应用页面编号 */
  applicationPageId?: number;

  /*表单数据 */
  data?: string;

  /*表单字段数据 */
  field?: string;
}
/**
 * 更新表单数据
 * @param {object} params 管理后台 - 表单数据新增/修改 Request VO
 * @param {number} params.id 表单数据编号
 * @param {number} params.applicationPageId 应用页面编号
 * @param {string} params.data 表单数据
 * @param {string} params.field 表单字段数据
 * @returns
 */
export function updateFormData(params: UpdateFormDataParams): Promise<any> {
  return put(`/admin-api/system/form-data/update`, params);
}

export interface UpdateBatchOperateParams {
  /*id*/
  id: number;
  /*应用页面编号 */
  applicationPageId?: number;
  /*api */
  api?: string;
}
/**
 * 批量操作修改
 */
export function updateBatchOperate(
  params: UpdateBatchOperateParams
): Promise<any> {
  return put(`/admin-api/system/page-batch-operate/update`, params);
}

/**
 * 创建数据源
 * @param {object} params 管理后台 - 数据源新增/修改 Request VO
 * @param {number} params.applicationId 应用编号
 * @param {number} params.type 类型(目前只有MySQL)
 * @param {number} params.applicantOrBackend 1应用内 2后台
 * @param {string} params.name 数据源名称
 * @param {number} params.connectionMethod 连接方式(1主机名连接 2JDBC连接)
 * @param {string} params.url JDBC URL
 * @param {string} params.host 主机地址
 * @param {string} params.port 端口
 * @param {string} params.databaseName 数据库名
 * @param {string} params.userName 用户名
 * @param {string} params.passWord 密码
 * @param {string} params.remark 备注
 * @returns
 */
// Parameter interface
export interface CreateDataSourceParams {
  /*应用编号 */
  applicationId?: number;

  /*类型(目前只有MySQL) */
  type?: number;

  /*1应用内 2后台 */
  applicantOrBackend?: number;

  /*数据源名称 */
  name?: string;

  /*连接方式(1主机名连接 2JDBC连接) */
  connectionMethod?: number;

  /*JDBC URL */
  url?: string;

  /*主机地址 */
  host?: string;

  /*端口 */
  port?: string;

  /*数据库名 */
  databaseName?: string;

  /*用户名 */
  userName?: string;

  /*密码 */
  passWord?: string;

  /*备注 */
  remark?: string;
}

export function createDataSource(params: CreateDataSourceParams): Promise<any> {
  return post(`/admin-api/system/data-source/create`, params);
}

/**
 * 获得数据源分页
 * @param {object} params
 * @param {string} params.applicationId 应用编号
 * @param {string}  params.applicantOrBackend 1应用内 2后台
 * @param {string} params.type 类型(目前只有MySQL)
 * @param {string} params.name 数据源名称
 * @param {string} params.connectionMethod 连接方式(1主机名连接 2JDBC连接)
 * @param {string} params.url JDBC URL
 * @param {string} params.host 主机地址
 * @param {string} params.port 端口
 * @param {string} params.databaseName 数据库名
 * @param {string} params.userName 用户名
 * @param {string} params.passWord 密码
 * @param {string} params.remark 备注
 * @param {string} params.createTime 创建时间
 * @param {string} params.pageNo 页码，从 1 开始
 * @param {string} params.pageSize 每页条数，最大值为 100
 * @returns
 */
interface getDataSourcePageParams {
  applicationId?: number; //应用编号
  applicantOrBackend?: number; //应用编号
  type?: string; //类型(目前只有MySQL)
  name?: string; //数据源名称
  connectionMethod?: number; //连接方式(1主机名连接 2JDBC连接)
  url?: string; //JDBC URL
  host?: string; //主机地址
  port?: string; //端口
  databaseName?: string; //数据库名
  userName?: string; //用户名
  passWord?: string; //密码
  remark?: string; //备注
  createTime?: string; //创建时间
  pageNo?: number; //页码，从 1 开始
  pageSize?: number; //每页条数，最大值为 100
}
export function getDataSourcePage(
  params: getDataSourcePageParams
): Promise<any> {
  return get(`/admin-api/system/data-source/page`, params);
}

/**
 * 删除数据源
 * @param {object} params
 * @param {string} params.id 编号
 * @returns
 */
interface DeleteDataSourceParams {
  id: number | string;
}
export function deleteDataSource(params: DeleteDataSourceParams): Promise<any> {
  return del(`/admin-api/system/data-source/delete`, params);
}
/**
 * 获得数据源
 * @param {object} params
 * @param {string} params.id 编号
 * @returns
 */
interface GetDataSourceParams {
  id: number | string;
}
export function getDataSource(params: GetDataSourceParams): Promise<any> {
  return get(`/admin-api/system/data-source/get`, params);
}

export interface UpdateDataSourceParams {
  /*主键 */
  id: number;

  /*应用编号 */
  applicationId?: number;

  /*类型(目前只有MySQL) */
  type?: number;

  /*1应用内 2后台 */
  applicantOrBackend?: number;

  /*数据源名称 */
  name?: string;

  /*连接方式(1主机名连接 2JDBC连接) */
  connectionMethod?: number;

  /*JDBC URL */
  url?: string;

  /*主机地址 */
  host?: string;

  /*端口 */
  port?: string;

  /*数据库名 */
  databaseName?: string;

  /*用户名 */
  userName?: string;

  /*密码 */
  passWord?: string;

  /*备注 */
  remark?: string;
}

/**
 * 更新数据源
 * @param {object} params 管理后台 - 数据源新增/修改 Request VO
 * @param {number} params.id 主键
 * @param {number} params.applicationId 应用编号
 * @param {number} params.type 类型(目前只有MySQL)
 * @param {number} params.applicantOrBackend 1应用内 2后台
 * @param {string} params.name 数据源名称
 * @param {number} params.connectionMethod 连接方式(1主机名连接 2JDBC连接)
 * @param {string} params.url JDBC URL
 * @param {string} params.host 主机地址
 * @param {string} params.port 端口
 * @param {string} params.databaseName 数据库名
 * @param {string} params.userName 用户名
 * @param {string} params.passWord 密码
 * @param {string} params.remark 备注
 * @returns
 */
export function updateDataSource(params: UpdateDataSourceParams): Promise<any> {
  return put(`/admin-api/system/data-source/update`, params);
}

/**
 * 测试数据库连接
 * @param {object} params
 * @param {number} params.applicationId 应用编号
 * @param {number} params.type 类型(目前只有MySQL)
 * @param {number} params.applicantOrBackend 1应用内 2后台
 * @param {string} params.name 数据源名称
 * @param {number} params.connectionMethod 连接方式(1主机名连接 2JDBC连接)
 * @param {string} params.url JDBC URL
 * @param {string} params.host 主机地址
 * @param {string} params.port 端口
 * @param {string} params.databaseName 数据库名
 * @param {string} params.userName 用户名
 * @param {string} params.passWord 密码
 * @param {string} params.remark 备注
 * @returns
 */

export interface TestDataSourceConnectionParams {
  /*应用编号 */
  applicationId?: number;

  /*类型(目前只有MySQL) */
  type?: number;

  /*1应用内 2后台 */
  applicantOrBackend?: number;

  /*数据源名称 */
  name?: string;

  /*连接方式(1主机名连接 2JDBC连接) */
  connectionMethod?: number;

  /*JDBC URL */
  url?: string;

  /*主机地址 */
  host?: string;

  /*端口 */
  port?: string;

  /*数据库名 */
  databaseName?: string;

  /*用户名 */
  userName?: string;

  /*密码 */
  passWord?: string;

  /*备注 */
  remark?: string;
}

export function testDataSourceConnection(
  params: TestDataSourceConnectionParams
): Promise<any> {
  return post(`/admin-api/system/data-source/test-connection`, params);
}

/**
 * 获取库中所有表
 * @param {object} params
 * @param {string} params.id  数据源编号Id
 * @returns
 */
interface GetTableListParams {
  id: number | string;
}
export function getTableList(params: GetTableListParams): Promise<any> {
  return get(`/admin-api/system/data-source/getTableList/${params.id}`);
}

/**
 * 获取表中所有字段
 * @param {string} id
 * @param {string} tableName
 * @returns
 */
interface GetTableColumnListParams {
  id: number | string;
  tableName: string;
}
export function getTableColumnList(
  params: GetTableColumnListParams
): Promise<any> {
  return get(
    `/admin-api/system/data-source/getTableColumnList/${params.id}/${params.tableName}`
  );
}

/**
 * 查询指定字段的数据
 * @param {object} params 查询字段数据 Request VO
 * @param {number} params.datasourceId 数据源ID
 * @param {string} params.tableName 表名
 * @param {array} params.columnNames 字段名列表，至少包含一个字段
 * @returns
 */
export interface QueryColumnDataParams {
  /*数据源ID */
  datasourceId?: number;

  /*表名 */
  tableName?: string;

  pageNo: number;
  pageSize: number;

  /*字段名列表，至少包含一个字段 */
  columnNames?: Record<string, unknown>[];
}
export function queryColumnData(params: QueryColumnDataParams): Promise<any> {
  return post(`/admin-api/system/data-source/query-column-data`, params);
}

// 流程表单：amis临时代码更新 /amis-code/create
// id	是	string	id 获取代码 get
// applicationPageId	是	string	页面id post
// jsonStr	是	string	保存代码 post
interface amisCodeUpdateData {
  id: string | number;
  applicationPageId: string | number;
  jsonStr: string;
}
export function amisCodeUpdate(data: amisCodeUpdateData): Promise<any> {
  return put(`/admin-api/system/amis-code/update`, data);
}

// 流程表单：amis临时代码获取 /amis-code/get
interface amisCodeGetData {
  applicationPageId: string | number;
}
export function amisCodeGet(data: amisCodeGetData): Promise<any> {
  return get(`/admin-api/system/amis-code/get`, data);
}

// 流程表单：wflow审批人代码保存 /Process/setOrGetCheckUserCode
// applicationPageId	是	string	页面id 保存数据 post，取数据 get
// jsonStr	是	string	保存代码 post
interface wflowApproveCodeGetData {
  applicationPageId: string | number;
}
export function wflowApproveCodeGet(
  data: wflowApproveCodeGetData
): Promise<any> {
  return get(`/admin-api/system/wflow-approve-code/get`, data);
}

interface wflowApproveCodeUpdateData {
  applicationPageId: string | number;
  id: string;
  wflowProcessId: string;
  settings: string;
  jsonStr: string;
}
export function wflowApproveCodeUpdate(
  data: wflowApproveCodeUpdateData
): Promise<any> {
  return put(`/admin-api/system/wflow-approve-code/update`, data);
}

/**
 * 获取控件字典列表
 * @returns 控件字典列表
 */
export function listAllControlDicts(): Promise<any> {
  return get('/admin-api/system/control-dict/list-all');
}

export interface GetControlDictPageParams {
  /*控件字典编号 */
  id?: number;

  /*控件名称 */
  controlType?: string;

  /*amis控件类型 */
  amisControlType?: string;

  /*分类 */
  classification?: string;

  /*json */
  json?: string;

  /*是否启用(1启用 0未启用) */
  status?: number;

  pageNo: number;

  pageSize: number;
}
/**
 * 获得控件字典分页
 * @param {string} controlType 控件名称
 * @param {string} amisControlType amis控件类型
 * @param {string} classification 分类
 * @param {string} json json
 * @param {string} status 是否启用(1启用 0未启用)
 * @param {string} pageNo 页码，从 1 开始
 * @param {string} pageSize 每页条数，最大值为 100
 * @returns
 */
export function GetControlDictPage(
  params: GetControlDictPageParams
): Promise<any> {
  return get(`/admin-api/system/control-dict/page`, params);
}

export interface UpdateControlDictParams {
  /*控件字典编号 */
  id: number;

  /*字段类型 */
  fieldType?: string;

  /*控件名称 */
  controlType?: string;

  /*amis控件类型 */
  amisControlType?: string;

  /*分类 */
  classification?: string;

  /*json */
  json?: string;

  /*是否启用(1启用 0未启用) */
  status?: number;
}
/**
 * 更新控件字典
 * @param {object} params 管理后台 - 控件字典新增/修改 Request VO
 * @param {number} params.id 控件字典编号
 * @param {string} params.fieldType 字段类型
 * @param {string} params.controlType 控件名称
 * @param {string} params.amisControlType amis控件类型
 * @param {string} params.classification 分类
 * @param {string} params.json json
 * @param {number} params.status 是否启用(1启用 0未启用)
 * @returns
 */
export function updateControlDict(
  params: UpdateControlDictParams
): Promise<any> {
  return put(`/admin-api/system/control-dict/update`, params);
}

export interface DeleteControlDictParams {
  id: number;
}
/**
 * 删除控件字典
 * @param {string} id 编号
 * @returns
 */
export function deleteControlDict(
  params: DeleteControlDictParams
): Promise<any> {
  return del(`/admin-api/system/control-dict/delete`, params);
}

// Parameter interface
export interface CreateControlDictParams {
  /*控件字典编号 */
  id: number;

  /*控件名称 */
  controlType?: string;

  /*amis控件类型 */
  amisControlType?: string;

  /*分类 */
  classification?: string;

  /*json */
  json?: string;

  /*是否启用(1启用 0未启用) */
  status?: number;
}

/**
 * 创建控件字典
 * @param {object} params 管理后台 - 控件字典新增/修改 Request VO
 * @param {number} params.id 控件字典编号
 * @param {string} params.controlType 控件名称
 * @param {string} params.amisControlType amis控件类型
 * @param {string} params.classification 分类
 * @param {string} params.json json
 * @param {number} params.status 是否启用(1启用 0未启用)
 * @returns
 */
export function createControlDict(
  params: CreateControlDictParams
): Promise<any> {
  return post(`/admin-api/system/control-dict/create`, params);
}

/**
 * 获取字典类型列表
 * @returns 控件字典列表
 */
export function listAllDicts(): Promise<any> {
  return get(
    '/admin-api/system/dict-type/page?pageNo=1&pageSize=100&name=&type='
  );
}

/**
 * 创建数据集
 * @param {object} params 管理后台 - 数据集新增/修改 Request VO
 * @param {number} params.applicationId 应用编号
 * @param {number} params.applicantOrBackend 1应用内 2后台
 * @param {string} params.name 数据集名称
 * @param {number} params.dataSourceId 数据源编号
 * @param {number} params.type 类型(1-原始数据源)
 * @param {number} params.source 来源(1-应用内表单 2-外部数据)
 * @param {string} params.belongingDataSource 所属数据源
 * @param {string} params.associatedDataTable 关联数据表
 * @param {string} params.config 字段JSON
 * @param {string} params.remarks 备注
 * @returns
 */
export interface CreateDataSetParams {
  /*应用编号 */
  applicationId?: number;

  /*1应用内 2后台 */
  applicantOrBackend?: number;

  /*数据集名称 */
  name?: string;

  /*数据源编号 */
  dataSourceId?: number;

  /*类型(1-原始数据源) */
  type?: number;

  /*来源(1-应用内表单 2-外部数据) */
  source?: number;

  /*所属数据源 */
  belongingDataSource?: string;

  /*关联数据表 */
  associatedDataTable?: string;

  /*字段JSON */
  config?: string;

  /*备注 */
  remarks?: string;
}
export function createDataSet(params: CreateDataSetParams): Promise<any> {
  return post(`/admin-api/system/data-set/create`, params);
}

/**
 * 获得数据集
 * @param {object} params 获得数据集
 * @param {number} params.id 数据集表编号
 * @returns
 */
export interface GetDataSetParams {
  id: number | string;
}

export function getDataSet(params: GetDataSetParams): Promise<any> {
  return get(`/admin-api/system/data-set/get`, params);
}

/**
 * 更新数据集
 * @param {object} params 管理后台 - 数据集新增/修改 Request VO
 * @param {number} params.id 数据集表编号
 * @param {number} params.applicationId 应用编号
 * @param {number} params.applicantOrBackend 1应用内 2后台
 * @param {string} params.name 数据集名称
 * @param {number} params.dataSourceId 数据源编号
 * @param {number} params.type 类型(1-原始数据源)
 * @param {number} params.source 来源(1-应用内表单 2-外部数据)
 * @param {string} params.belongingDataSource 所属数据源
 * @param {string} params.associatedDataTable 关联数据表
 * @param {string} params.config 字段JSON
 * @param {string} params.remarks 备注
 * @returns
 */
export interface UpdateDataSetParams {
  /*数据集表编号 */
  id: number;

  /*应用编号 */
  applicationId?: number;

  /*1应用内 2后台 */
  applicantOrBackend?: number;

  /*数据集名称 */
  name?: string;

  /*数据源编号 */
  dataSourceId?: number;

  /*类型(1-原始数据源) */
  type?: number;

  /*来源(1-应用内表单 2-外部数据) */
  source?: number;

  /*所属数据源 */
  belongingDataSource?: string;

  /*关联数据表 */
  associatedDataTable?: string;

  /*字段JSON */
  config?: string;

  /*备注 */
  remarks?: string;
}

export function updateDataSet(params: UpdateDataSetParams): Promise<any> {
  return put(`/admin-api/system/data-set/update`, params);
}

/**
 * 获得数据集分页
 * @param {object} params
 * @param {string} params.applicationId 应用编号
 * @param {string} params.applicantOrBackend 1应用内 2后台
 * @param {string} params.name 数据集名称
 * @param {string} params.dataSourceId 数据源编号
 * @param {string} params.type 类型(1-原始数据源)
 * @param {string} params.source 来源(1-应用内表单 2-外部数据)
 * @param {string} params.belongingDataSource 所属数据源
 * @param {string} params.associatedDataTable 关联数据表
 * @param {string} params.config 字段JSON
 * @param {string} params.remarks 备注
 * @param {string} params.createTime 创建时间
 * @param {string} params.pageNo 页码，从 1 开始
 * @param {string} params.pageSize 每页条数，最大值为 100
 * @returns
 */
export interface getDataSetPageParams {
  /*应用编号 */
  applicationId?: number | string;

  /*1应用内 2后台 */
  applicantOrBackend?: number | string;

  /*数据集名称 */
  name?: string;

  /*数据源编号 */
  dataSourceId?: number;

  /*类型(1-原始数据源) */
  type?: number;

  /*来源(1-应用内表单 2-外部数据) */
  source?: number;

  /*所属数据源 */
  belongingDataSource?: string;

  /*关联数据表 */
  associatedDataTable?: string;

  /*创建时间 */
  createTime?: string;

  /*页码，从 1 开始 */
  pageNo?: number;

  /*每页条数，最大值为 100 */
  pageSize?: number;
}
export function getDataSetPage(params: getDataSetPageParams): Promise<any> {
  return get(`/admin-api/system/data-set/page`, params);
}

/**
 * 获得表单字段值分页
 * @param {object} params
 * @param {string} params.applicationPageId
 * @param {string} params.page
 * @param {string} params.perPage
 * @returns
 */
export interface getFormFieldValuePageParams {
  applicationPageId: number | string;
  pageNo: number;
  pageSize: number;
}
export function getFormFieldValuePage(
  params: getFormFieldValuePageParams
): Promise<any> {
  return get(
    `/admin-api/system/form-field-value/page/${params.applicationPageId}?pageNo=${params.pageNo}&pageSize=${params.pageSize}`
  );
}

/**
 * 获得数据集字段分页
 * @param {object} params
 * @param {string} params.applicationId 应用编号
 * @param {string} params.dataSetId 数据集表编号
 * @param {string} params.controlDictId 控件编号
 * @param {string} params.sourceTable 数据源表(应用内表单是存页面编号)
 * @param {string} params.fieldName 字段名称
 * @param {string} params.fieldDesc 字段描述
 * @param {string} params.sort 排序
 * @param {string} params.createTime 创建时间
 * @param {string} params.pageNo 页码，从 1 开始
 * @param {string} params.pageSize 每页条数，最大值为 100
 * @returns
 */
export interface getDataSetFieldPageParams {
  applicationId?: number | string;
  dataSetId?: number | string;
  controlDictId?: number | string;
  sourceTable?: number | string;
  fieldName?: number | string;
  fieldDesc?: number | string;
  sort?: number | string;
  createTime?: number | string;
  pageNo: number | string;
  pageSize: number | string;
}
export function getDataSetFieldPage(
  params: getDataSetFieldPageParams
): Promise<any> {
  return get(`/admin-api/system/data-set-field/page`, params);
}

// 登录接口/login/login
interface loginData {
  username: string;
  password: string;
  captcha: string | number;
  key: string;
  remember: boolean | string;
}
const login = (data: loginData): Promise<any> => post('/login/login', data);
export {login};

// 获取用户信息
const getUserInfo = (): Promise<any> => get('/index/getUserInfo');
export {getUserInfo};

// /team/getTeamList
// team_id	否	string	团队里的项目下的应用
interface TeamListData {
  business_id: string | number;
  team_id?: string;
}
const getTeamList = (data: TeamListData): Promise<any> =>
  get('/team/getTeamList', data);
export {getTeamList};

// 创建团队
// /Team/createTeam
interface createTeamData {
  business_id: string;
  team_name: string;
  depict: string;
}
const createTeam = (data: createTeamData): Promise<any> =>
  post('/Team/createTeam', data);
export {createTeam};

// 编辑团队/Team/editTeam GET 编辑回显（查看?） POST 提交保存
interface editTeamData {
  id: string | number;
  team_name: string;
  depict: string;
}
const editTeam = (data: editTeamData): Promise<any> =>
  post('/Team/editTeam', data);
export {editTeam};

// 获取团队详情/Team/editTeam GET
interface showTeamData {
  id: string | number;
}
const showTeam = (data: showTeamData): Promise<any> =>
  get('/Team/editTeam', data);
export {showTeam};

// 删除团队/Team/delTeam  POST
interface delTeamData {
  id: string | number;
  // team_name: string;
}
const delTeam = (data: delTeamData): Promise<any> =>
  del('/admin-api/system/team-and-project/delete', data);
export {delTeam};

// 创建项目(分组) /Team/createGroup
interface createGroupData {
  team_id: string; //团队id
  group_name: string; //组名
  depict: string; //描述
}

const createGroup = (data: createGroupData): Promise<any> =>
  post('/Team/createGroup', data);
export {createGroup};

// 编辑项目(分组)/Group/editGroup
// id	是	string	GET 编辑回显 POST 提交修改
// group_name	是	string	分组名称
// schema	是	string	json 代码
// depict	否	string	描述
interface editGroupData {
  id: string | number; //团队id
  group_name: string; //分组名称
  schema: string; //json 代码
  depict?: string; //描述
}
const editGroup = (data: editGroupData): Promise<any> =>
  post('/Group/editGroup', data);
export {editGroup};

// 获取项目(分组)详情 /Group/editGroup
interface showGroupData {
  id: string | number; //团队id
}
const showGroup = (data: showGroupData): Promise<any> =>
  get('/Group/editGroup', data);
export {showGroup};

// 删除项目(分组)/Group/delGroup
interface delGroupData {
  id: string | number; //团队id
}
const delGroup = (data: delGroupData): Promise<any> =>
  post('/Group/delGroup', data);
export {delGroup};

// 创建应用 Apply/createApply
interface createApplyData {
  team_id: string; //团队id
  group_id: string; //分组id
  apply_name: string; //应用名称
  depict?: string; //描述
  icon?: string; //图标
}
const createApply = (data: createApplyData): Promise<any> =>
  post('/Apply/createApply', data);
export {createApply};

// 获取应用分组和我创建的应用列表 获取项目下的回收站列表Apply/getTeamByApplyList
interface TeamByApplyListData {
  team_id: string; //团队id
  group_id?: string; //分组id
  is_release?: string; //0：未发布 1:已发布
  is_move?: string; //1:移出
  is_create_user?: string; //1:只看我创建的
}
const getTeamByApplyList = (data: TeamByApplyListData): Promise<any> =>
  get('/Apply/getTeamByApplyList', data);
export {getTeamByApplyList};

// 获取团队下的回收站列表team/getTeamApplyRecycleList
interface TeamApplyRecycleListData {
  team_id: string; //团队id
}
const getTeamApplyRecycleList = (
  data: TeamApplyRecycleListData
): Promise<any> => get('/team/getTeamApplyRecycleList', data);
export {getTeamApplyRecycleList};

// 编辑应用 /Apply/editApply
interface editApplyData {
  id: string; //应用id  回显数据get,提交post
  team_id: string; //团队id
  group_id: string; //分组id
  apply_name: string; //应用名称
  depict?: string; //描述
  icon?: string; //图标
}
const editApply = (data: editApplyData): Promise<any> =>
  post('/Apply/editApply', data);
export {editApply};

// 查看应用 /Apply/editApply
interface showApplyData {
  id: string; //团队id 回显数据get
}
const showApply = (data: showApplyData): Promise<any> =>
  get('/Apply/editApply', data);
export {showApply};

// 创建页面分类 page_class/createPageClass
interface createPageClassData {
  apply_id: string | number; //应用id
  name: string; //类名
  page_id?: string; //页面id
}
const createPageClass = (data: createPageClassData): Promise<any> =>
  post('/page_class/createPageClass', data);
export {createPageClass};

// /PageClass/editPageClass 编辑页面分类
// id	是	string	 POST 提交修改
// apply_id	是	string	应用id
// name	是	string	分类名
interface editPageClassData {
  id: string; //分类id
  apply_id: string | number; //应用id
  name: string; //类名
}
const editPageClass = (data: editPageClassData): Promise<any> =>
  post('/page_class/editPageClass', data);
export {editPageClass};

// /PageClass/editPageClass 获取页面分类数据 GET 编辑回显
interface showPageClassData {
  id: string; //分类id
}
const showPageClass = (data: showPageClassData): Promise<any> =>
  get('/page_class/editPageClass', data);
export {showPageClass};

// 取消页面分类归属 /Route/cancelPageClass
interface cancelPageClassData {
  id: string; //分类id
}
const cancelPageClass = (data: cancelPageClassData): Promise<any> =>
  post('/page_class/cancelPageClass', data);
export {cancelPageClass};

// 删除页面分类 /page_class/delPageClass
interface delPageClassData {
  id: string; //分类id
}
const delPageClass = (data: delPageClassData): Promise<any> =>
  post('/page_class/delPageClass', data);
export {delPageClass};

// 获取路由列表
interface RuteListData {
  apply_id: string; // 应用id
}
const getRuteList = (data: RuteListData): Promise<any> =>
  get('/route/getRuteList', data);
export {getRuteList};

// 创建路由
interface createRuteData {
  // team_id	是	string	团队id
  team_id: string;
  // group_id	是	string	分组id
  group_id: string;
  // apply_id	是	string	应用id
  apply_id: string;
  // page_class_id	是	string	页面分类id
  page_class_id: string;
  // label	是	string	标题
  label: string;
  // path	是	string	路由路径
  path?: string;
  // type	是	string	页面类型 0:目录 1:普通表单 2:流程 3:excel导入
  type: string | number;
  // parent_id	否	string	上级目录id
  parent_id?: string;
  // icon	否	string	图标
  icon?: string;
}
const createRute = (data: createRuteData): Promise<any> =>
  post('/route/createRute', data);
export {createRute};

// 删除路由
interface ruteDeleteData {
  id: string | number;
}
const ruteDelete = (data: ruteDeleteData): Promise<any> =>
  post('/route/ruteDelete', data);
export {ruteDelete};

// 路由详情
interface ruteDetailsData {
  id: string | number;
}
const ruteDetails = (data: ruteDetailsData): Promise<any> =>
  get('/route/ruteDetails', data);
export {ruteDetails};

// 编辑路由 /route/editRute
interface editRuteData {
  id: string | number;
  // team_id	否	string	团队id
  team_id?: string;
  // group_id	否	string	分组id
  group_id?: string;
  // apply_id	否	string	应用id
  apply_id?: string;
  // page_class_id	否	string	页面分类id
  page_class_id?: string;
  // label	否	string	标题
  label?: string;
  // path	否	string	路由路径
  path?: string;
  // type	否	string	页面类型 0:目录 1:普通表单 2:流程 3:excel导入
  type?: string | number;
  // parent_id	否	string	上级目录id
  parent_id?: string;
  // icon	否	string	图标
  icon?: string;
}
const editRute = (data: editRuteData): Promise<any> =>
  post('/route/editRute', data);
export {editRute};

// 编辑路由页面-保存生成的代码
interface ruteCodeSaveData {
  id: string | number;
  schema: string;
}
const ruteCodeSave = (data: ruteCodeSaveData): Promise<any> =>
  post('/generate_code/codeSave', data);
export {ruteCodeSave};

// 保存字段
interface fieldSaveData {
  table_name: string | number; //表名
  field: string; //json代码 [{"type":"input-text","name":"name","label":"name"},{"type":"input-text","name":"code","label":"code"}]
}
const fieldSaveTable = (data: fieldSaveData): Promise<any> =>
  post('/generate_code/fieldSave', data);
export {fieldSaveTable};

// 创建报表、仪表盘
// /report/createDashboardReport
// name	是	string	名称
// orderNum	是	string	排序
interface createDashboardReportData {
  name: string;
  orderNum: string | number;
}
const createDashboardReport = (data: createDashboardReportData): Promise<any> =>
  post('/report/createDashboardReport', data);
export {createDashboardReport};

// 报表、仪表盘code保存 /generate_code/savePageTypeCode
// id	是	string	页面id
// code	是	string	code码,通过add创建接口返回
interface savePageTypeCodeData {
  id: string | number;
  code: string;
}
const savePageTypeCode = (data: savePageTypeCodeData): Promise<any> =>
  post('/generate_code/savePageTypeCode', data);
export {savePageTypeCode};

// 流程表单：amis临时代码保存和获取 /Process/setOrGetAmisCode
// id	是	string	id 获取代码 get
// page_id	是	string	页面id post
// jsonStr	是	string	保存代码 post
// interface setOrGetAmisCodeData {
//   id?: string | number;
//   applicationPageId: string | number;
//   jsonStr: string;
// }
// const setOrGetAmisCode = (data: setOrGetAmisCodeData): Promise<any> =>
//   post('/admin-api/system/amis-code/create', data);
// export {setOrGetAmisCode};

// // 流程表单：wflow审批人代码保存 /Process/setOrGetCheckUserCode
// // page_id	是	string	页面id 保存数据 post，取数据 get
// // jsonStr	是	string	保存代码 post
// interface setOrGetCheckUserCodeData {
//   page_id: string | number;
//   jsonStr?: string;
// }
// const setOrGetCheckUserCode = (data: setOrGetCheckUserCodeData): Promise<any> =>
//   get('/Process/setOrGetCheckUserCode', data);
// export {setOrGetCheckUserCode};

// 创建wiki文档 /Wiki/createWiki
// item_type	是	string
// item_name	是	string
// item_description	是	string
// item_domain	是	string
// password	是	string
// user_token	是	string	用户token
interface createWikiData {
  item_type: string | number;
  item_name: string;
  user_token: string;
  apply_id: string | number;

  item_description: string;
  item_domain: string;
  password: string;
}
const createWiki = (data: createWikiData): Promise<any> =>
  post('/Wiki/createWiki', data);
export {createWiki};

// https://showdoc.ykddm.fjpipixia.com/server/index.php?s=/api/user/loginByVerify
const wikiUserToken = () =>
  get(
    'https://showdoc.ykddm.fjpipixia.com/server/index.php?s=/api/user/loginByVerify'
  );

// 页面排序 /route/ruteSort
interface ruteSortData {
  data: string; //[,{"id":127,"sort":3,"page_type":1,"page_class_id":0},{"id":40,"sort":5,"page_type":2,"page_class_id":40}]
}
const ruteSort = (data: ruteSortData): Promise<any> =>
  post('/route/ruteSort', data);
export {ruteSort};

// Dataset/getShowField
interface getShowFieldData {
  id: string | number;
}
const getShowField = (data: getShowFieldData): Promise<any> =>
  get('/Dataset/getShowField', data);
export {getShowField};

//Route/updateDatasetIdBindingRute
interface updateDatasetIdBindingRuteData {
  id: string | number;
  dataset_id: string | number;
}
const updateDatasetIdBindingRute = (
  data: updateDatasetIdBindingRuteData
): Promise<any> => post('/Route/updateDatasetIdBindingRute', data);
export {updateDatasetIdBindingRute};

//system/application/update
interface updateApplicationData {
  id: string | number;
  isEnable: number;
  isRelease: number;
  isPublishBuildCenter: number;
}

const updateApplicationReleaseOrEnable = (
  data: updateApplicationData
): Promise<any> => put('/admin-api/system/application/update', data);
export {updateApplicationReleaseOrEnable};

//创建已发布应用分组
export interface CreatePublishApplicationGroupParams {
  name: string;
  userIds?: string;
  applicationIds?: string;
  sort?: number;
}
export function createPublishApplicationGroup(
  params: CreatePublishApplicationGroupParams
): Promise<any> {
  return post('/admin-api/system/publish-application-groups/create', params);
}

//删除已发布应用分组
export interface DeletePublishApplicationGroupParams {
  id: number;
}
export function deletePublishApplicationGroup(
  params: DeletePublishApplicationGroupParams
): Promise<any> {
  return del('/admin-api/system/publish-application-groups/delete', params);
}

//导出已发布应用分组 Excel
export function exportPublishApplicationGroupExcel(): Promise<any> {
  return get('/admin-api/system/publish-application-groups/export-excel');
}

//获得已发布应用分组

export interface GetPublishApplicationGroupParams {
  id: number;
}
export function getPublishApplicationGroup(
  params: GetPublishApplicationGroupParams
): Promise<any> {
  return get('/admin-api/system/publish-application-groups/get', params);
}

// 获得已发布应用分组列表
export interface GetPublishApplicationGroupListParams {
  userId?: number;
}
export function getPublishApplicationGroupList(params: GetPublishApplicationGroupListParams): Promise<any> {
  return get('/admin-api/system/publish-application-groups/list',params);
}

//获得已发布应用分组分页
export interface GetPublishApplicationGroupPageParams {
  name?: string;
  pageNo: number;
  pageSize: number;
}
export function getPublishApplicationGroupPage(
  params: GetPublishApplicationGroupPageParams
): Promise<any> {
  return get('/admin-api/system/publish-application-groups/page', params);
}

//更新已发布应用分组
export interface UpdatePublishApplicationGroupParams {
  id: number;
  name?: string;
  userIds?: string;
  applicationIds?: string;
  sort?: number;
}
export function updatePublishApplicationGroup(
  params: UpdatePublishApplicationGroupParams
): Promise<any> {
  return put('/admin-api/system/publish-application-groups/update', params);
}

// 获得图片管理分页
export interface GetImageManagementPageParams {
  pageNo: number;
  pageSize: number;
  isImageManagement?: number;
  imageManagementType?: number;
}

export function getImageManagementPage(
  params: GetImageManagementPageParams
): Promise<any> {
  return get('/admin-api/infra/file/page', params);
}

// 获得表单数据的版本列表
export interface GetFormDataVersionListParams {
  applicationPageId: string | number;
  versionName?: string;
  userId?: string | number;
}
export function getFormDataVersionList(
  params: GetFormDataVersionListParams
): Promise<any> {
  return get('/admin-api/system/form-data/get-version-list', params);
}

// 获取应用页面权限分页列表
export interface GetApplicationPagePermissionListParams {
  pageNo: number;
  pageSize: number;
  applicationPageId?: number | string;
  type?: string|number;
}

/**
 * 获取应用页面权限分页列表
 * @param params 查询参数
 * @returns Promise<any>
 */
export function getApplicationPagePermissionList(
  params: GetApplicationPagePermissionListParams
): Promise<any> {
  return get('/admin-api/system/application-page-permission/page', params);
}

/**
 * 创建应用页面权限
 * @param params 创建参数
 * @returns Promise<any>
 */
export interface CreateApplicationPagePermissionParams {
  applicationPageId: number | string;
  type: string|number;
  name: string;
  remark: string;
  userIds: string;
  [key: string]: any; // 可根据实际参数补充字段
}

export function createApplicationPagePermission(
  params: CreateApplicationPagePermissionParams
): Promise<any> {
  return post('/admin-api/system/application-page-permission/create', params);
}
  

/**
 * 删除应用页面权限
 * @param id 应用页面权限ID
 * @returns Promise<any>
 */
export function deleteApplicationPagePermission(id: number | string): Promise<any> {
  return del(`/admin-api/system/application-page-permission/delete?id=${id}`);
}


export interface GetSystemComponentListParams {
  pageNo: number;
  pageSize: number;
}

/**
 * 获取应用页面权限分页列表
 * @param params 查询参数
 * @returns Promise<any>
 */
export function getSystemComponentList(
  params: GetSystemComponentListParams
): Promise<any> {
  return get('/admin-api/system/component-manage/page', params);
}
import React from 'react';
import {ToastComponent, AlertComponent, Spinner} from 'amis';
/**
 * BrowserRouter: history 路由模式
 * HashRouter: hash 路由模式
 */
import {Route, Switch, Redirect, HashRouter as Router} from 'react-router-dom';
import {observer} from 'mobx-react';
import {IMainStore} from '@/store/index';
import '@/renderer/LineChartRenderer';
import '@/renderer/BarChartRenderer';
import '@/renderer/CusIconSelect';
import '@/renderer/FunnelChartRenderer';
import '@/renderer/PieChartRenderer';
import '@/renderer/RadarChartRenderer';
import '@/renderer/GaugeChartRenderer';
import '@/renderer/MapChartRenderer';
import '@/renderer/WordCloudChartRenderer';
import '@/renderer/CalendarHeatmapChartRenderer';
import '@/renderer/IconSelector';
import '@/renderer/IconShow';
import '@/renderer/TreeSelectMemberRenderer';
import '@/renderer/TreeSelectRoleRenderer';
import '@/renderer/MultipleTreeSelectMemberRenderer';
import '@/renderer/MultipleTreeSelectRoleRenderer';
import '@/renderer/TreeSelectDeptRenderer';
import '@/renderer/TreeSelectPosRenderer';
import '@/renderer/MultipleTreeSelectDeptRenderer';
import '@/renderer/MultipleTreeSelectPosRenderer';
import '@/renderer/InputAvatar';
import '@/renderer/InputAppIcon/InputAppIcon';
import '@/renderer/AppIcon/AppIcon';
import '@/renderer/InputAddress';
import '@/renderer/InputPhone';
import '@/renderer/MyNumber';
import '@/renderer/Relatedfiled';
import '@/renderer/MyCarouselRenderer';
import '@/renderer/MyDataCardContainerRenderer';
import '@/renderer/MyPortletRenderer/MyPortletRenderer';
import '@/renderer/MyApp';
import '@/renderer/MyDrawer';
import '@/renderer/MyDialog';
import '@/renderer/MyAmis';
import '@/renderer/MyInputVerificationCode';
import '@/renderer/MyGridNav';
import '@/renderer/MyShape';
import '@/renderer/MySpinner';
import '@/renderer/MyInputExcel';
import '@/renderer/MyNav/MyNav';
import '@/renderer/MyRadio';
import '@/renderer/MyJsonSchema';
import '@/renderer/MyJsonSchemaEditor';
import '@/renderer/ComponentViewer';
import '@/renderer/MediaCardRenderer';
import '@/renderer/UserCardRenderer';
import '@/renderer/IconCardRenderer';
import '@/renderer/CRUDDatasetRenderer';
import '@/renderer/CRUD2DatasetRenderer';
import '@/renderer/CRUD2DatasetTable';
import '@/renderer/RelatedformTable';
import '@/renderer/Serial';

import ForgetPassword from '@/views/forget-password';
import VerifyCode from '@/views/forget-password/verify-code';
import ResetPassword from '@/views/forget-password/reset-password';
import ResetSuccess from '@/views/forget-password/reset-success';
import controlEditor from '@/views/controlEditor/index';
import SystemComponent from '@/views/systemComponent/index';
import OrgComponent from '@/views/orgComponent';

const ApplyPage = React.lazy(() => import('@/views/applyPage/index'));
const Editor = React.lazy(() => import('@/views/editor/index'));
const CrudEditor = React.lazy(() => import('@/views/editor/CrudEditor'));
const ComponentTempEditor = React.lazy(() => import('@/views/editor/componentTempEditor'));
const FormTempEditor = React.lazy(() => import('@/views/editor/formTempEditor'));
const ControlEditor = React.lazy(() => import('@/views/controlEditor/index'));
const PortletEditor = React.lazy(() => import('@/views/portletEditor/index'));
const Login = React.lazy(() => import('@/views/login'));
const workbench = React.lazy(() => import('@/views/workbench/index'));
const appbuild = React.lazy(() => import('@/views/appbuild/index'));
const SubmissionPage = React.lazy(() => import('@/views/submission/index'));
const resourceCenter = React.lazy(() => import('@/views/resourceCenter/index'));
const ProcessSubmissionPage = React.lazy(
  () => import('@/views/processSubmission/index')
);
const DataSubmissionPage = React.lazy(
  () => import('@/views/DataSubmission/index')
);
const BindWx = React.lazy(() => import('@/views/bindWx/index'));

const appSetting = React.lazy(() => import('@/views/applySettings/index'));
const fullScreen = React.lazy(
  () => import('@/views/applySettings/fullScreen/index')
);

const manage = React.lazy(() => import('@/views/manage/index'));
const platformManage = React.lazy(() => import('@/views/platformManage/index'));
// const zeroCodeManage = React.lazy(() => import('@/views/zeroCodeManage/index'));
export default observer(function ({store}: {store: IMainStore}) {
  return (
    <Router>
      <div className="routes-wrapper">
        <ToastComponent key="toast" position={'top-right'} />
        <AlertComponent key="alert" />
        <React.Suspense
          fallback={<Spinner overlay className="m-t-lg" size="lg" />}
        >
          <Switch>
            {/* 登录页 */}
            <Route path="/login" component={Login} />

            {/* 忘记密码相关路由 */}
            <Route
              path="/forget-password/reset-success"
              component={ResetSuccess}
            />
            <Route
              path="/forget-password/reset-password"
              component={ResetPassword}
            />
            <Route path="/forget-password/verify-code" component={VerifyCode} />
            <Route path="/forget-password" component={ForgetPassword} />

            {/* 系统组件管理路由 */}
            <Route path="/system-component" component={SystemComponent} />
            <Route path="/orgcomponent" component={OrgComponent} />

            {/* 应用相关路由 - 提前匹配以确保优先级 */}
            <Route path="/app:id/submission/:form" component={SubmissionPage} />
            <Route
              path="/app:id/processSubmission/:form"
              component={ProcessSubmissionPage}
            />
            <Route
              path="/app:id/DataSubmission/:form"
              component={DataSubmissionPage}
            />
            <Route path="/app:appId/design/:form" component={Editor} />
            <Route path="/app:appId/editcrud/:form" component={CrudEditor} />

            <Route path="/ControlEditor/:id" component={ControlEditor} />
            <Route path="/portletEditor:appId/design/:form" component={PortletEditor} />
            <Route path="/componentTempEditor/:id" component={ComponentTempEditor} />
            <Route path="/formTempEditor/:id" component={FormTempEditor} />

            {/* 管理相关路由 */}
            <Route path={'/organization/manage/:menu/:submenu'} component={manage} />
            <Route path={'/organization/manage/page:page'} component={manage} />
            <Redirect from="/organization/manage" to="/organization/manage/company" exact />
            <Route path={'/organization/manage/:menu'} component={manage} />
            <Route path={'/organization/manage'} component={manage} />

            <Route path={'/platform/manage/:menu'} component={platformManage} />
            <Route path={'/platform/manage'} component={platformManage} />
            <Redirect from="/platform/manage" to="/platform/manage/company" exact />

            <Route
              path={'/platform/team:team/project:project'}
              component={workbench}
            />
            <Route path={'/platform/team:team'} component={workbench} />
            <Redirect from="/platform" to="/platform/workbench" exact />
            <Route path={'/platform/:menu'} component={workbench} />
            <Route path={'/platform'} component={workbench} />

            <Route
              path={'/appbuild/team:team/project:project'}
              component={appbuild}
            />
            <Route path={'/appbuild/team:team'} component={appbuild} />
            {/* <Redirect from="/appbuild" to="/appbuild/" exact /> */}
            <Route path={'/appbuild/:menu'} component={appbuild} />
            <Route path={'/appbuild'} component={appbuild} />

            {/* 初始页 */}
            <Redirect from="/" to={`/platform`} exact />

            <Route path={'/resourceCenter'} component={resourceCenter} />


            <Route
              path={'/app:appId/:playType/:form/:appSetMenu/:fullScreen'}
              component={ApplyPage}
            />
            <Route
              path={'/app:appId/:playType/:form/:appSetMenu'}
              component={ApplyPage}
            />
            <Route path={'/app:appId/:playType/:form'} component={ApplyPage} />
            <Route path={'/app:appId/:playType'} component={ApplyPage} />
            {/* submission admin */}

            {/* 绑定微信 */}
            <Route path="/bindWx" component={BindWx} />

            {/* 企业设置 */}
            {/* <Route path="*" component={} /> */}
          </Switch>
        </React.Suspense>
      </div>
    </Router>
  );
});
